<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\DuyVC\app\src\main\res"/><source path="D:\TienDuong\DuyVC\app\build\generated\res\rs\debug"/><source path="D:\TienDuong\DuyVC\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\DuyVC\app\src\main\res"><file name="bottom_nav_color" path="D:\TienDuong\DuyVC\app\src\main\res\color\bottom_nav_color.xml" qualifiers="" type="color"/><file name="auth_background" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\auth_background.xml" qualifiers="" type="drawable"/><file name="bg_date_circle" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\bg_date_circle.xml" qualifiers="" type="drawable"/><file name="bg_input_field" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\bg_input_field.xml" qualifiers="" type="drawable"/><file name="bg_selected_color" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\bg_selected_color.xml" qualifiers="" type="drawable"/><file name="bg_selected_date" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\bg_selected_date.xml" qualifiers="" type="drawable"/><file name="bg_step_status" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\bg_step_status.xml" qualifiers="" type="drawable"/><file name="button_outline" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\button_outline.xml" qualifiers="" type="drawable"/><file name="button_primary" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="category_background" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\category_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="color_selector_blue" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\color_selector_blue.xml" qualifiers="" type="drawable"/><file name="color_selector_green" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\color_selector_green.xml" qualifiers="" type="drawable"/><file name="color_selector_orange" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\color_selector_orange.xml" qualifiers="" type="drawable"/><file name="color_selector_pink" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\color_selector_pink.xml" qualifiers="" type="drawable"/><file name="color_selector_purple" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\color_selector_purple.xml" qualifiers="" type="drawable"/><file name="color_selector_red" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\color_selector_red.xml" qualifiers="" type="drawable"/><file name="header_gradient" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\header_gradient.xml" qualifiers="" type="drawable"/><file name="icon_selector_background" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\icon_selector_background.xml" qualifiers="" type="drawable"/><file name="ic_add" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_app_logo" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_app_logo.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_drag_handle" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_drag_handle.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_email" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_email.xml" qualifiers="" type="drawable"/><file name="ic_filter" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_filter.xml" qualifiers="" type="drawable"/><file name="ic_help" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_help.xml" qualifiers="" type="drawable"/><file name="ic_info" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_lock" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_lock.xml" qualifiers="" type="drawable"/><file name="ic_login_illustration" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_login_illustration.xml" qualifiers="" type="drawable"/><file name="ic_person" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_search" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_statistics" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_statistics.xml" qualifiers="" type="drawable"/><file name="ic_tasks" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\ic_tasks.xml" qualifiers="" type="drawable"/><file name="priority_background" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\priority_background.xml" qualifiers="" type="drawable"/><file name="priority_badge" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\priority_badge.xml" qualifiers="" type="drawable"/><file name="rounded_background" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\TienDuong\DuyVC\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_auth" path="D:\TienDuong\DuyVC\app\src\main\res\layout\activity_auth.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\TienDuong\DuyVC\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="dialog_add_category" path="D:\TienDuong\DuyVC\app\src\main\res\layout\dialog_add_category.xml" qualifiers="" type="layout"/><file name="dialog_add_task" path="D:\TienDuong\DuyVC\app\src\main\res\layout\dialog_add_task.xml" qualifiers="" type="layout"/><file name="dialog_login" path="D:\TienDuong\DuyVC\app\src\main\res\layout\dialog_login.xml" qualifiers="" type="layout"/><file name="dialog_task_detail" path="D:\TienDuong\DuyVC\app\src\main\res\layout\dialog_task_detail.xml" qualifiers="" type="layout"/><file name="fragment_calendar" path="D:\TienDuong\DuyVC\app\src\main\res\layout\fragment_calendar.xml" qualifiers="" type="layout"/><file name="fragment_login" path="D:\TienDuong\DuyVC\app\src\main\res\layout\fragment_login.xml" qualifiers="" type="layout"/><file name="fragment_profile" path="D:\TienDuong\DuyVC\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="fragment_register" path="D:\TienDuong\DuyVC\app\src\main\res\layout\fragment_register.xml" qualifiers="" type="layout"/><file name="fragment_task_list" path="D:\TienDuong\DuyVC\app\src\main\res\layout\fragment_task_list.xml" qualifiers="" type="layout"/><file name="item_category" path="D:\TienDuong\DuyVC\app\src\main\res\layout\item_category.xml" qualifiers="" type="layout"/><file name="item_task" path="D:\TienDuong\DuyVC\app\src\main\res\layout\item_task.xml" qualifiers="" type="layout"/><file name="item_task_step_detail" path="D:\TienDuong\DuyVC\app\src\main\res\layout\item_task_step_detail.xml" qualifiers="" type="layout"/><file name="item_task_step_edit" path="D:\TienDuong\DuyVC\app\src\main\res\layout\item_task_step_edit.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="D:\TienDuong\DuyVC\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="category_menu" path="D:\TienDuong\DuyVC\app\src\main\res\menu\category_menu.xml" qualifiers="" type="menu"/><file name="menu_main" path="D:\TienDuong\DuyVC\app\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\TienDuong\DuyVC\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="nav_graph" path="D:\TienDuong\DuyVC\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="D:\TienDuong\DuyVC\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary">#2196F3</color><color name="error">#F44336</color><color name="primary_dark">#1976D2</color><color name="accent">#FF4081</color><color name="border_selected">#2196F3</color><color name="background_light">#FAFAFA</color><color name="border_light">#E0E0E0</color><color name="background_selected">#E3F2FD</color><color name="primary_light">#E3F2FD</color><color name="success">#4CAF50</color><color name="warning">#FF9800</color></file><file path="D:\TienDuong\DuyVC\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="fab_margin">16dp</dimen><dimen name="match_parent">-1dp</dimen></file><file path="D:\TienDuong\DuyVC\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Task Manager</string><string name="action_settings">Settings</string><string name="first_fragment_label">Task Manager</string><string name="second_fragment_label">Calendar</string><string name="next">Next</string><string name="previous">Previous</string><string name="hello_first_fragment">Hello first fragment</string><string name="hello_second_fragment">Hello second fragment. Arg: %1$s</string><string name="task_manager_title">Task Manager</string><string name="search">Tìm kiếm</string><string name="category_all">Tất cả</string><string name="category_personal">Cá nhân</string><string name="category_work">Công việc</string><string name="add_category">+ Thêm chủ đề</string><string name="add_task">Thêm task mới</string><string name="nav_tasks">Nhiệm Vụ</string><string name="nav_calendar">Lịch</string><string name="nav_profile">Của tôi</string><string name="task_detail">Chi tiết Task</string><string name="edit">Chỉnh sửa</string><string name="complete_task">Hoàn thành</string><string name="delete_task">Xóa</string><string name="status_in_progress">Đang thực hiện</string><string name="status_completed">Hoàn thành</string><string name="status_pending">Chờ xử lý</string></file><file path="D:\TienDuong\DuyVC\app\src\main\res\values\styles.xml" qualifiers=""><style name="CalendarViewCustom" parent="android:Theme.Material.Light">
        <item name="android:colorAccent">#2196F3</item>
        <item name="android:textColorPrimary">#333333</item>
    </style></file><file path="D:\TienDuong\DuyVC\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style><style name="Theme.MyApplication.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.MyApplication.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="Theme.MyApplication.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/></file><file path="D:\TienDuong\DuyVC\app\src\main\res\values-land\dimens.xml" qualifiers="land"><dimen name="fab_margin">48dp</dimen></file><file path="D:\TienDuong\DuyVC\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="D:\TienDuong\DuyVC\app\src\main\res\values-w1240dp\dimens.xml" qualifiers="w1240dp-v13"><dimen name="fab_margin">200dp</dimen></file><file path="D:\TienDuong\DuyVC\app\src\main\res\values-w600dp\dimens.xml" qualifiers="w600dp-v13"><dimen name="fab_margin">48dp</dimen></file><file name="backup_rules" path="D:\TienDuong\DuyVC\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\TienDuong\DuyVC\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="icon_selector_ripple" path="D:\TienDuong\DuyVC\app\src\main\res\drawable\icon_selector_ripple.xml" qualifiers="" type="drawable"/></source><source path="D:\TienDuong\DuyVC\app\build\generated\res\rs\debug"/><source path="D:\TienDuong\DuyVC\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\DuyVC\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\TienDuong\DuyVC\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>
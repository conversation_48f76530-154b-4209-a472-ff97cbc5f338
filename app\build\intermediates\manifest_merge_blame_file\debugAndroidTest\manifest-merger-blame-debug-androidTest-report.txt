1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication.test" >
4
5    <uses-sdk
5-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:5:5-74
6        android:minSdkVersion="21"
6-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:5:15-41
7        android:targetSdkVersion="34" />
7-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:5:42-71
8
9    <instrumentation
9-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:11:5-15:75
10        android:name="androidx.test.runner.AndroidJUnitRunner"
10-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:11:22-76
11        android:functionalTest="false"
11-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:14:22-52
12        android:handleProfiling="false"
12-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:13:22-53
13        android:label="Tests for com.example.myapplication"
13-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:15:22-73
14        android:targetPackage="com.example.myapplication" />
14-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:12:22-71
15
16    <uses-permission android:name="android.permission.REORDER_TASKS" />
16-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:5-72
16-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:24:22-69
17
18    <queries>
18-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d23614a03381efe4c49a3da05bc44d04\transformed\runner-1.5.2\AndroidManifest.xml:24:5-28:15
19        <package android:name="androidx.test.orchestrator" />
19-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d23614a03381efe4c49a3da05bc44d04\transformed\runner-1.5.2\AndroidManifest.xml:25:9-62
19-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d23614a03381efe4c49a3da05bc44d04\transformed\runner-1.5.2\AndroidManifest.xml:25:18-59
20        <package android:name="androidx.test.services" />
20-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d23614a03381efe4c49a3da05bc44d04\transformed\runner-1.5.2\AndroidManifest.xml:26:9-58
20-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d23614a03381efe4c49a3da05bc44d04\transformed\runner-1.5.2\AndroidManifest.xml:26:18-55
21        <package android:name="com.google.android.apps.common.testing.services" />
21-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d23614a03381efe4c49a3da05bc44d04\transformed\runner-1.5.2\AndroidManifest.xml:27:9-83
21-->[androidx.test:runner:1.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\d23614a03381efe4c49a3da05bc44d04\transformed\runner-1.5.2\AndroidManifest.xml:27:18-80
22    </queries>
23
24    <application android:debuggable="true" >
24-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:7:5-9:19
25        <uses-library android:name="android.test.runner" />
25-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:8:9-60
25-->D:\TienDuong\DuyVC\app\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest3623267148826814890.xml:8:23-57
26
27        <activity
27-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:27:9-34:20
28            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
28-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:28:13-99
29            android:exported="true"
29-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:29:13-36
30            android:theme="@style/WhiteBackgroundTheme" >
30-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:30:13-56
31            <intent-filter android:priority="-100" >
31-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
31-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
32                <category android:name="android.intent.category.LAUNCHER" />
32-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:32:17-77
32-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:32:27-74
33            </intent-filter>
34        </activity>
35        <activity
35-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:35:9-42:20
36            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
36-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:36:13-95
37            android:exported="true"
37-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:37:13-36
38            android:theme="@style/WhiteBackgroundTheme" >
38-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:38:13-56
39            <intent-filter android:priority="-100" >
39-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
39-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
40                <category android:name="android.intent.category.LAUNCHER" />
40-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:32:17-77
40-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:32:27-74
41            </intent-filter>
42        </activity>
43        <activity
43-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:43:9-50:20
44            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
44-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:44:13-103
45            android:exported="true"
45-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:45:13-36
46            android:theme="@style/WhiteBackgroundDialogTheme" >
46-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:46:13-62
47            <intent-filter android:priority="-100" >
47-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:13-33:29
47-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:31:28-51
48                <category android:name="android.intent.category.LAUNCHER" />
48-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:32:17-77
48-->[androidx.test:core:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\892f965f08de27f483ff1a062a804a13\transformed\jetified-core-1.5.0\AndroidManifest.xml:32:27-74
49            </intent-filter>
50        </activity>
51    </application>
52
53</manifest>

package com.example.myapplication.fragments;

import android.app.AlertDialog;
import android.app.DatePickerDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.text.Editable;
import android.text.TextWatcher;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapplication.R;
import com.example.myapplication.adapters.TaskAdapter;
import com.example.myapplication.adapters.CategoryAdapter;
import com.example.myapplication.adapters.TaskStepEditAdapter;
import com.example.myapplication.adapters.TaskStepDetailAdapter;
import com.example.myapplication.auth.AuthManager;
import com.example.myapplication.database.TodoDatabase;
import com.example.myapplication.database.entities.CategoryEntity;
import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.database.entities.TaskStepEntity;
import com.example.myapplication.models.Task;
import com.example.myapplication.models.Category;
import com.example.myapplication.models.TaskStep;
import com.example.myapplication.viewmodel.TodoViewModel;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.chip.Chip;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.UUID;

public class TaskListFragment extends Fragment {

    private RecyclerView recyclerViewTasks;
    private TaskAdapter taskAdapter;
    private FloatingActionButton fabAddTask;
    private Spinner spinnerCategoryFilter;
    private Button btnAddCategory;
    private CheckBox cbShowIncompleteOnly;
    private EditText etSearch;

    private List<Task> taskList;
    private List<Category> categoryList;
    private String selectedCategoryId = null;
    private String searchQuery = "";
    private boolean showIncompleteOnly = false;
    private boolean isUpdatingFromUI = false;

    // Room Database components
    private TodoViewModel todoViewModel;
    private AuthManager authManager;
    private List<TaskEntity> taskEntityList;
    private List<CategoryEntity> categoryEntityList;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_task_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Initialize ViewModel and AuthManager
        todoViewModel = new ViewModelProvider(this).get(TodoViewModel.class);
        authManager = AuthManager.getInstance(requireContext());

        initViews(view);
        setupRecyclerViews();
        setupFab();
        setupCategorySpinner();
        setupAddCategoryButton();
        setupCompletionFilter();
        setupSearch();
        observeData();
    }

    private void initViews(View view) {
        recyclerViewTasks = view.findViewById(R.id.recycler_view_tasks);
        fabAddTask = view.findViewById(R.id.fab_add_task);
        spinnerCategoryFilter = view.findViewById(R.id.spinner_category_filter);
        btnAddCategory = view.findViewById(R.id.btn_add_category);
        cbShowIncompleteOnly = view.findViewById(R.id.cb_show_incomplete_only);
        etSearch = view.findViewById(R.id.et_search);
    }



    private void setupRecyclerViews() {
        // Initialize empty task list
        taskList = new ArrayList<>();

        // Setup tasks RecyclerView
        taskAdapter = new TaskAdapter(taskList);
        recyclerViewTasks.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewTasks.setAdapter(taskAdapter);

        taskAdapter.setOnTaskClickListener(new TaskAdapter.OnTaskClickListener() {
            @Override
            public void onTaskClick(Task task) {
                // Open task detail dialog
                showTaskDetailDialog(task);
            }

            @Override
            public void onTaskToggle(Task task, boolean isCompleted) {
                // Cập nhật trạng thái hoàn thành task trong database
                if (task.getId() != null) {
                    // Đặt flag để tránh observer trigger filter
                    isUpdatingFromUI = true;

                    // Cập nhật task object ngay lập tức để giữ UI nhất quán
                    task.setCompleted(isCompleted);

                    // Cập nhật task cụ thể trong adapter
                    taskAdapter.updateTask(task);

                    // Tự động hoàn thành/bỏ hoàn thành tất cả steps khi task completion thay đổi
                    if (task.getSteps() != null && !task.getSteps().isEmpty()) {
                        for (TaskStep step : task.getSteps()) {
                            step.setCompleted(isCompleted);
                        }
                    }

                    TodoDatabase.databaseWriteExecutor.execute(() -> {
                        try {
                            int taskId = Integer.parseInt(task.getId());
                            TaskEntity taskEntity = new TaskEntity();
                            taskEntity.setId(taskId);
                            taskEntity.setTitle(task.getTitle());
                            taskEntity.setDescription(task.getDescription());
                            taskEntity.setCategoryId(Integer.parseInt(task.getCategory().getId()));
                            taskEntity.setPriority(task.getPriority());
                            taskEntity.setCompleted(isCompleted);
                            taskEntity.setDueDate(task.getDueDate());
                            taskEntity.setStartTime(task.getStartTime());
                            taskEntity.setEndTime(task.getEndTime());
                            taskEntity.setCreatedAt(task.getCreatedAt());
                            taskEntity.setUserId(authManager.getCurrentUserId());

                            android.util.Log.d("TaskListFragment", "Updating task - ID: " + taskId + ", User: " + authManager.getCurrentUserId() + ", Completed: " + isCompleted);
                            todoViewModel.getRepository().updateTaskSync(taskEntity);

                            // Cập nhật trạng thái hoàn thành của tất cả steps trong database
                            if (task.getSteps() != null && !task.getSteps().isEmpty()) {
                                for (TaskStep step : task.getSteps()) {
                                    if (step.getId() != null) {
                                        try {
                                            int stepId = Integer.parseInt(step.getId());
                                            TaskStepEntity stepEntity = new TaskStepEntity();
                                            stepEntity.setId(stepId);
                                            stepEntity.setTaskId(taskId);
                                            stepEntity.setTitle(step.getTitle());
                                            stepEntity.setDescription(step.getDescription());
                                            stepEntity.setCompleted(isCompleted);
                                            stepEntity.setStepOrder(step.getOrder());
                                            stepEntity.setCreatedAt(step.getCreatedAt());

                                            todoViewModel.getRepository().updateTaskStepSync(stepEntity);
                                        } catch (NumberFormatException e) {
                                            // Bỏ qua step ID không hợp lệ
                                        }
                                    }
                                }
                            }

                            // Show toast on main thread and reset flag
                            if (getActivity() != null) {
                                getActivity().runOnUiThread(() -> {
                                    isUpdatingFromUI = false; // Reset flag

                                    Toast.makeText(getContext(), isCompleted ? "Đã hoàn thành!" : "Chưa hoàn thành", Toast.LENGTH_SHORT).show();
                                });
                            }
                        } catch (NumberFormatException e) {
                            // Handle invalid ID - revert the change
                            if (getActivity() != null) {
                                getActivity().runOnUiThread(() -> {
                                    task.setCompleted(!isCompleted);
                                    taskAdapter.updateTask(task);
                                    Toast.makeText(getContext(), "Lỗi cập nhật nhiệm vụ", Toast.LENGTH_SHORT).show();
                                });
                            }
                        }
                    });
                }
            }
        });


    }

    private void setupFab() {
        fabAddTask.setOnClickListener(v -> showAddTaskDialog());
    }

    private void setupAddCategoryButton() {
        btnAddCategory.setOnClickListener(v -> showAddCategoryDialog());
    }

    private void setupCompletionFilter() {
        cbShowIncompleteOnly.setOnCheckedChangeListener((buttonView, isChecked) -> {
            showIncompleteOnly = isChecked;
            android.util.Log.d("TaskListFragment", "Completion filter changed: " + isChecked);
            applyCompletionFilter();
        });
    }

    private void setupSearch() {
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                searchQuery = s.toString().trim();
                filterTasks();
            }
        });
    }

    private void setupCategorySpinner() {
        // Create category list with "All" option
        List<Category> spinnerCategories = new ArrayList<>();
        spinnerCategories.add(new Category("all", "Tất cả", "#2196F3", "📋"));

        if (categoryList != null) {
            spinnerCategories.addAll(categoryList);
        }

        // Create custom adapter for spinner
        ArrayAdapter<Category> spinnerAdapter = new ArrayAdapter<Category>(getContext(), android.R.layout.simple_spinner_item, spinnerCategories) {
            @Override
            public View getView(int position, View convertView, ViewGroup parent) {
                TextView view = (TextView) super.getView(position, convertView, parent);
                Category category = getItem(position);
                if (category != null) {
                    view.setText(category.getIcon() + " " + category.getName());
                }
                return view;
            }

            @Override
            public View getDropDownView(int position, View convertView, ViewGroup parent) {
                TextView view = (TextView) super.getDropDownView(position, convertView, parent);
                Category category = getItem(position);
                if (category != null) {
                    view.setText(category.getIcon() + " " + category.getName());
                }
                return view;
            }
        };

        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategoryFilter.setAdapter(spinnerAdapter);

        // Set selection listener
        spinnerCategoryFilter.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                Category selectedCategory = (Category) parent.getItemAtPosition(position);
                if (selectedCategory.getId().equals("all")) {
                    selectedCategoryId = null;
                } else {
                    selectedCategoryId = selectedCategory.getId();
                }
                filterTasks();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
                selectedCategoryId = null;
                filterTasks();
            }
        });
    }

    private void applyCompletionFilter() {
        if (taskList == null || taskAdapter == null) return;

        android.util.Log.d("TaskListFragment", "applyCompletionFilter called - showIncompleteOnly: " + showIncompleteOnly);

        List<Task> filteredTasks = new ArrayList<>();
        for (Task task : taskList) {
            // Only apply completion filter
            if (!showIncompleteOnly || !task.isCompleted()) {
                filteredTasks.add(task);
            }
        }

        // Sort tasks: incomplete tasks first, then by priority
        filteredTasks.sort((task1, task2) -> {
            // First sort by completion status (incomplete first)
            if (task1.isCompleted() != task2.isCompleted()) {
                return task1.isCompleted() ? 1 : -1;
            }
            // Then sort by priority (HIGH > MEDIUM > LOW)
            return task2.getPriority().ordinal() - task1.getPriority().ordinal();
        });

        taskAdapter.updateTasks(filteredTasks);
        android.util.Log.d("TaskListFragment", "applyCompletionFilter completed - filtered tasks: " + filteredTasks.size());
    }

// sắp xếp
    private void filterTasks() {
        if (taskList == null || taskAdapter == null) return;

        android.util.Log.d("TaskListFragment", "filterTasks called - showIncompleteOnly: " + showIncompleteOnly);

        List<Task> filteredTasks = new ArrayList<>();
        for (Task task : taskList) {
            boolean matchesCategory = selectedCategoryId == null ||
                (task.getCategory() != null && task.getCategory().getId().equals(selectedCategoryId));

            boolean matchesSearch = searchQuery.isEmpty() ||
                task.getTitle().toLowerCase().contains(searchQuery.toLowerCase()) ||
                task.getDescription().toLowerCase().contains(searchQuery.toLowerCase());

            // Always show all tasks regardless of completion status
            boolean matchesCompletion = true;

            if (matchesCategory && matchesSearch && matchesCompletion) {
                filteredTasks.add(task);
            }
        }

        // Sort tasks: incomplete tasks first, then by priority
        filteredTasks.sort((task1, task2) -> {
            // First sort by completion status (incomplete first)
            if (task1.isCompleted() != task2.isCompleted()) {
                return task1.isCompleted() ? 1 : -1;
            }
            // Then sort by priority (HIGH > MEDIUM > LOW)
            return task2.getPriority().ordinal() - task1.getPriority().ordinal();
        });

        taskAdapter.updateTasks(filteredTasks);
        android.util.Log.d("TaskListFragment", "filterTasks completed - filtered tasks: " + filteredTasks.size());
    }

    private void showAddTaskDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_task, null);
        
        EditText etTitle = dialogView.findViewById(R.id.et_task_title);
        EditText etDescription = dialogView.findViewById(R.id.et_task_description);
        Spinner spinnerCategory = dialogView.findViewById(R.id.spinner_category);
        Spinner spinnerPriority = dialogView.findViewById(R.id.spinner_priority);
        Button btnAddStep = dialogView.findViewById(R.id.btn_add_step);
        RecyclerView recyclerViewSteps = dialogView.findViewById(R.id.recycler_view_steps);

        TextView tvStartDateTime = dialogView.findViewById(R.id.tv_start_datetime);
        Button btnSelectStartDateTime = dialogView.findViewById(R.id.btn_select_start_datetime);
        TextView tvEndDateTime = dialogView.findViewById(R.id.tv_end_datetime);
        Button btnSelectEndDateTime = dialogView.findViewById(R.id.btn_select_end_datetime);

        // Selected date/time variables
        final Date[] selectedStartDateTime = {null};
        final Date[] selectedEndDateTime = {null};

        // Setup category spinner with real data from database
        List<Category> categories = new ArrayList<>();
        if (categoryEntityList != null && !categoryEntityList.isEmpty()) {
            for (CategoryEntity entity : categoryEntityList) {
                categories.add(new Category(
                    String.valueOf(entity.getId()),
                    entity.getName(),
                    entity.getColor(),
                    entity.getIcon()
                ));
            }
        }
        ArrayAdapter<Category> categoryAdapter = new ArrayAdapter<>(getContext(), android.R.layout.simple_spinner_item, categories);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCategory.setAdapter(categoryAdapter);

        // Setup priority spinner
        ArrayAdapter<Task.Priority> priorityAdapter = new ArrayAdapter<>(getContext(), android.R.layout.simple_spinner_item, Task.Priority.values());
        priorityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerPriority.setAdapter(priorityAdapter);

        // Setup steps RecyclerView
        List<TaskStep> stepList = new ArrayList<>();
        TaskStepEditAdapter stepAdapter = new TaskStepEditAdapter(stepList, new TaskStepEditAdapter.OnStepActionListener() {
            @Override
            public void onStepDeleted(int position) {
                stepList.remove(position);
                notifyDataSetChanged();
            }

            @Override
            public void onStepChanged(int position, TaskStep step) {
                // Step is already updated in the list
            }

            private void notifyDataSetChanged() {
                // Refresh the adapter
                TaskStepEditAdapter currentAdapter = (TaskStepEditAdapter) recyclerViewSteps.getAdapter();
                if (currentAdapter != null) {
                    currentAdapter.updateSteps(stepList);
                }
            }
        });

        // Thiết lập RecyclerView cho steps trong dialog thêm task
        recyclerViewSteps.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewSteps.setAdapter(stepAdapter);
        recyclerViewSteps.setNestedScrollingEnabled(true); // Bật nested scrolling để hoạt động tốt với NestedScrollView (tránh xung đột scroll)

        // Xử lý click nút thêm step
        btnAddStep.setOnClickListener(v -> {
            TaskStep newStep = new TaskStep();
            newStep.setId(String.valueOf(System.currentTimeMillis()));
            newStep.setTitle("");
            newStep.setDescription("");
            newStep.setOrder(stepList.size());
            stepList.add(newStep);
            stepAdapter.updateSteps(stepList);

            // Scroll xuống để thấy step mới được thêm (đảm bảo user thấy step vừa tạo)
            recyclerViewSteps.post(() -> {
                recyclerViewSteps.smoothScrollToPosition(stepList.size() - 1);
            });
        });



        // Start datetime picker setup
        btnSelectStartDateTime.setOnClickListener(v -> {
            Calendar calendar = Calendar.getInstance();

            // First show date picker
            DatePickerDialog datePickerDialog = new DatePickerDialog(
                getContext(),
                (dateView, year, month, dayOfMonth) -> {
                    Calendar selectedCalendar = Calendar.getInstance();
                    selectedCalendar.set(year, month, dayOfMonth);

                    // Then show time picker
                    android.app.TimePickerDialog timePickerDialog = new android.app.TimePickerDialog(
                        getContext(),
                        (timeView, hourOfDay, minute) -> {
                            selectedCalendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
                            selectedCalendar.set(Calendar.MINUTE, minute);
                            selectedCalendar.set(Calendar.SECOND, 0);
                            selectedCalendar.set(Calendar.MILLISECOND, 0);

                            selectedStartDateTime[0] = selectedCalendar.getTime();
                            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
                            tvStartDateTime.setText("� " + sdf.format(selectedStartDateTime[0]));
                        },
                        calendar.get(Calendar.HOUR_OF_DAY),
                        calendar.get(Calendar.MINUTE),
                        true // 24 hour format
                    );
                    timePickerDialog.show();
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
            );

            // Set minimum date to today
            datePickerDialog.getDatePicker().setMinDate(System.currentTimeMillis());
            datePickerDialog.show();
        });

        // End datetime picker setup
        btnSelectEndDateTime.setOnClickListener(v -> {
            Calendar calendar = Calendar.getInstance();

            // First show date picker
            DatePickerDialog datePickerDialog = new DatePickerDialog(
                getContext(),
                (dateView, year, month, dayOfMonth) -> {
                    Calendar selectedCalendar = Calendar.getInstance();
                    selectedCalendar.set(year, month, dayOfMonth);

                    // Then show time picker
                    android.app.TimePickerDialog timePickerDialog = new android.app.TimePickerDialog(
                        getContext(),
                        (timeView, hourOfDay, minute) -> {
                            selectedCalendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
                            selectedCalendar.set(Calendar.MINUTE, minute);
                            selectedCalendar.set(Calendar.SECOND, 0);
                            selectedCalendar.set(Calendar.MILLISECOND, 0);

                            // Validate end datetime is after start datetime
                            if (selectedStartDateTime[0] != null && selectedCalendar.getTime().before(selectedStartDateTime[0])) {
                                Toast.makeText(getContext(), "Thời gian kết thúc phải sau thời gian bắt đầu", Toast.LENGTH_SHORT).show();
                                return;
                            }

                            selectedEndDateTime[0] = selectedCalendar.getTime();
                            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
                            tvEndDateTime.setText("� " + sdf.format(selectedEndDateTime[0]));
                        },
                        calendar.get(Calendar.HOUR_OF_DAY),
                        calendar.get(Calendar.MINUTE),
                        true // 24 hour format
                    );
                    timePickerDialog.show();
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
            );

            // Set minimum date to today
            datePickerDialog.getDatePicker().setMinDate(System.currentTimeMillis());
            datePickerDialog.show();
        });

        builder.setView(dialogView)
                .setTitle("Thêm nhiệm vụ mới")
                .setPositiveButton("Thêm", (dialog, which) -> {
                    String title = etTitle.getText().toString().trim();
                    String description = etDescription.getText().toString().trim();
                    Category category = (Category) spinnerCategory.getSelectedItem();
                    Task.Priority priority = (Task.Priority) spinnerPriority.getSelectedItem();

                    if (!title.isEmpty()) {
                        // Check if start time is selected (optional validation)
                        if (selectedStartDateTime[0] != null && selectedEndDateTime[0] != null) {
                            if (selectedEndDateTime[0].before(selectedStartDateTime[0])) {
                                Toast.makeText(getContext(), "Thời gian kết thúc phải sau thời gian bắt đầu", Toast.LENGTH_SHORT).show();
                                return;
                            }
                        }

                        // Filter out empty steps
                        List<TaskStep> validSteps = new ArrayList<>();
                        for (TaskStep step : stepList) {
                            if (step.getTitle() != null && !step.getTitle().trim().isEmpty()) {
                                validSteps.add(step);
                            }
                        }
                        addTaskToDatabase(title, description, category, priority, selectedStartDateTime[0], selectedEndDateTime[0], validSteps);
                    }
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

    private void observeData() {
        int currentUserId = authManager.getCurrentUserId();

        // Observe tasks from database for current user
        todoViewModel.getTasksByUser(currentUserId).observe(getViewLifecycleOwner(), taskEntities -> {
            if (taskEntities != null) {
                android.util.Log.d("TaskListFragment", "Observer triggered - isUpdatingFromUI: " + isUpdatingFromUI + ", tasks count: " + taskEntities.size());
                taskEntityList = taskEntities;
                // Only refresh if not updating from UI to prevent filter trigger
                if (!isUpdatingFromUI) {
                    android.util.Log.d("TaskListFragment", "Refreshing tasks from observer");
                    refreshTasksWithCategories();
                } else {
                    android.util.Log.d("TaskListFragment", "Skipping refresh - updating from UI");
                }
            }
        });

        // Observe categories from database for current user (including default categories)
        todoViewModel.getCategoriesForUser(currentUserId).observe(getViewLifecycleOwner(), categoryEntities -> {
            if (categoryEntities != null) {
                categoryEntityList = categoryEntities;
                // Convert CategoryEntity to Category for existing adapter
                categoryList = new ArrayList<>();
                for (CategoryEntity entity : categoryEntities) {
                    categoryList.add(entity.toCategory());
                }

                // Update spinner
                setupCategorySpinner();
                // Refresh tasks with proper categories
                refreshTasksWithCategories();
            }
        });
    }

    private void refreshTasksWithCategories() {
        if (taskEntityList != null && categoryEntityList != null) {
            taskList = new ArrayList<>();
            for (TaskEntity entity : taskEntityList) {
                // Find the corresponding category
                Category category = null;
                for (CategoryEntity catEntity : categoryEntityList) {
                    if (catEntity.getId() == entity.getCategoryId()) {
                        category = catEntity.toCategory();
                        break;
                    }
                }

                // If no category found, use default
                if (category == null) {
                    category = new Category("0", "Chung", "#2196F3", "📝");
                }

                Task task = entity.toTask(category);
                taskList.add(task);
            }
            // Apply current filters (category and search only)
            filterTasks();

            // Apply completion filter if enabled
            if (showIncompleteOnly) {
                applyCompletionFilter();
            }
        }
    }



    private void addTaskToDatabase(String title, String description, Category category, Task.Priority priority, Date startTime, Date endTime, List<TaskStep> steps) {
        // Create TaskEntity directly
        TaskEntity taskEntity = new TaskEntity();
        taskEntity.setTitle(title);
        taskEntity.setDescription(description);
        taskEntity.setDueDate(endTime); // Use end time as due date for compatibility
        taskEntity.setStartTime(startTime);
        taskEntity.setEndTime(endTime);
        taskEntity.setPriority(priority);
        taskEntity.setCompleted(false);
        taskEntity.setCreatedAt(new Date());
        taskEntity.setUserId(authManager.getCurrentUserId());

        // Find category ID, default to 1 if not found
        int categoryId = 1;
        if (category != null) {
            try {
                categoryId = Integer.parseInt(category.getId());
            } catch (NumberFormatException e) {
                // If category ID is not a number, try to find by name
                if (categoryEntityList != null && !categoryEntityList.isEmpty()) {
                    for (CategoryEntity entity : categoryEntityList) {
                        if (entity.getName().equals(category.getName())) {
                            categoryId = entity.getId();
                            break;
                        }
                    }
                }
            }
        }
        taskEntity.setCategoryId(categoryId);

        // Insert task to database first, then insert steps
        TodoDatabase.databaseWriteExecutor.execute(() -> {
            // Insert task and get the generated ID
            long taskId = todoViewModel.getRepository().insertTaskAndGetId(taskEntity);

            // Insert steps if any
            if (steps != null && !steps.isEmpty()) {
                for (int i = 0; i < steps.size(); i++) {
                    TaskStep step = steps.get(i);
                    if (step.getTitle() != null && !step.getTitle().trim().isEmpty()) {
                        TaskStepEntity stepEntity = new TaskStepEntity();
                        stepEntity.setTaskId((int) taskId);
                        stepEntity.setTitle(step.getTitle());
                        stepEntity.setDescription(step.getDescription());
                        stepEntity.setCompleted(step.isCompleted());
                        stepEntity.setStepOrder(i);
                        stepEntity.setCreatedAt(new Date());

                        todoViewModel.getRepository().insertTaskStepSync(stepEntity);
                    }
                }
            }
        });

        Toast.makeText(getContext(), "Đã thêm nhiệm vụ mới với " + steps.size() + " bước!", Toast.LENGTH_SHORT).show();
    }

    private void showTaskDetailDialog(Task task) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_task_detail, null);

        // Initialize views
        TextView tvTaskTitle = dialogView.findViewById(R.id.tv_task_title);
        TextView tvTaskDescription = dialogView.findViewById(R.id.tv_task_description);
        com.google.android.material.chip.Chip chipCategory = dialogView.findViewById(R.id.chip_category);
        com.google.android.material.chip.Chip chipPriority = dialogView.findViewById(R.id.chip_priority);
        TextView tvStartTimeDetail = dialogView.findViewById(R.id.tv_start_time_detail);
        TextView tvEndTimeDetail = dialogView.findViewById(R.id.tv_end_time_detail);
        LinearLayout layoutProgress = dialogView.findViewById(R.id.layout_progress);
        ProgressBar progressBar = dialogView.findViewById(R.id.progress_bar);
        TextView tvProgressText = dialogView.findViewById(R.id.tv_progress_text);
        Button btnAddStepDetail = dialogView.findViewById(R.id.btn_add_step_detail);
        RecyclerView recyclerViewStepsDetail = dialogView.findViewById(R.id.recycler_view_steps_detail);
        TextView tvNoSteps = dialogView.findViewById(R.id.tv_no_steps);

        // Set task information
        tvTaskTitle.setText(task.getTitle());

        if (task.getDescription() != null && !task.getDescription().trim().isEmpty()) {
            tvTaskDescription.setText(task.getDescription());
            tvTaskDescription.setVisibility(View.VISIBLE);
        }

        // Set category with proper color
        if (task.getCategory() != null) {
            chipCategory.setText(task.getCategory().getName());
            chipCategory.setChipBackgroundColor(android.content.res.ColorStateList.valueOf(task.getCategory().getColorInt()));
        } else {
            chipCategory.setText("Chung");
            chipCategory.setChipBackgroundColor(android.content.res.ColorStateList.valueOf(android.graphics.Color.parseColor("#2196F3")));
        }

        // Set priority with proper color
        chipPriority.setText(task.getPriority().getDisplayName());
        chipPriority.setChipBackgroundColor(android.content.res.ColorStateList.valueOf(android.graphics.Color.parseColor(task.getPriority().getColor())));

        // Set time information
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
        if (task.getStartTime() != null) {
            tvStartTimeDetail.setText("� Bắt đầu: " + dateTimeFormat.format(task.getStartTime()));
        } else {
            tvStartTimeDetail.setText("� Bắt đầu: --/--/-- --:--");
        }

        if (task.getEndTime() != null) {
            tvEndTimeDetail.setText("� Kết thúc: " + dateTimeFormat.format(task.getEndTime()));
        } else {
            tvEndTimeDetail.setText("� Kết thúc: --/--/-- --:--");
        }

        // Setup steps - Load from database
        List<TaskStep> steps = new ArrayList<>();

        // Load steps from database if task has ID
        if (task.getId() != null) {
            try {
                int taskId = Integer.parseInt(task.getId());
                todoViewModel.getStepsByTaskId(taskId).observe(getViewLifecycleOwner(), stepEntities -> {
                    steps.clear();
                    if (stepEntities != null) {
                        for (TaskStepEntity entity : stepEntities) {
                            TaskStep step = new TaskStep();
                            step.setId(String.valueOf(entity.getId()));
                            step.setTitle(entity.getTitle());
                            step.setDescription(entity.getDescription());
                            step.setCompleted(entity.isCompleted());
                            step.setOrder(entity.getStepOrder());
                            step.setCreatedAt(entity.getCreatedAt());
                            steps.add(step);
                        }
                    }

                    // Update UI
                    if (!steps.isEmpty()) {
                        layoutProgress.setVisibility(View.VISIBLE);
                        tvNoSteps.setVisibility(View.GONE);

                        // Calculate progress
                        int completedSteps = 0;
                        for (TaskStep step : steps) {
                            if (step.isCompleted()) {
                                completedSteps++;
                            }
                        }
                        int totalSteps = steps.size();
                        float progress = totalSteps > 0 ? (float) completedSteps / totalSteps * 100f : 0f;

                        progressBar.setMax(100);
                        progressBar.setProgress((int) progress);
                        tvProgressText.setText(completedSteps + "/" + totalSteps + " bước hoàn thành");
                    } else {
                        layoutProgress.setVisibility(View.GONE);
                        tvNoSteps.setVisibility(View.VISIBLE);
                    }

                    // Update adapter
                    TaskStepDetailAdapter currentAdapter = (TaskStepDetailAdapter) recyclerViewStepsDetail.getAdapter();
                    if (currentAdapter != null) {
                        currentAdapter.updateSteps(steps);
                    }
                });
            } catch (NumberFormatException e) {
                // Handle invalid task ID
                layoutProgress.setVisibility(View.GONE);
                tvNoSteps.setVisibility(View.VISIBLE);
            }
        } else {
            layoutProgress.setVisibility(View.GONE);
            tvNoSteps.setVisibility(View.VISIBLE);
        }

        // Setup steps RecyclerView
        TaskStepDetailAdapter stepAdapter = new TaskStepDetailAdapter(steps, new TaskStepDetailAdapter.OnStepActionListener() {
            @Override
            public void onStepCompletionChanged(int position, TaskStep step, boolean isCompleted) {
                // Set flag to prevent observer from triggering filter
                isUpdatingFromUI = true;
                android.util.Log.d("TaskListFragment", "Step completion changed - setting isUpdatingFromUI = true");

                // Update progress
                int completedSteps = task.getCompletedStepsCount();
                int totalSteps = task.getTotalStepsCount();
                float progress = task.getProgressPercentage();

                progressBar.setProgress((int) progress);
                tvProgressText.setText(completedSteps + "/" + totalSteps + " bước hoàn thành");

                // Update step in database
                if (step.getId() != null) {
                    TodoDatabase.databaseWriteExecutor.execute(() -> {
                        try {
                            int stepId = Integer.parseInt(step.getId());
                            TaskStepEntity stepEntity = new TaskStepEntity();
                            stepEntity.setId(stepId);
                            stepEntity.setTaskId(Integer.parseInt(task.getId()));
                            stepEntity.setTitle(step.getTitle());
                            stepEntity.setDescription(step.getDescription());
                            stepEntity.setCompleted(step.isCompleted());
                            stepEntity.setStepOrder(step.getOrder());
                            stepEntity.setCreatedAt(step.getCreatedAt());

                            todoViewModel.getRepository().updateTaskStepSync(stepEntity);

                            // Update task completion status if all steps are completed
                            boolean allStepsCompleted = true;
                            for (TaskStep s : steps) {
                                if (!s.isCompleted()) {
                                    allStepsCompleted = false;
                                    break;
                                }
                            }

                            boolean taskShouldBeCompleted = allStepsCompleted && !steps.isEmpty();

                            // Update task object immediately for UI consistency
                            task.setCompleted(taskShouldBeCompleted);

                            // Update task in adapter immediately
                            if (getActivity() != null) {
                                getActivity().runOnUiThread(() -> {
                                    if (taskAdapter != null) {
                                        taskAdapter.updateTask(task);
                                    }
                                });
                            }

                            // Update task completion status in database
                            TaskEntity taskEntity = new TaskEntity();
                            taskEntity.setId(Integer.parseInt(task.getId()));
                            taskEntity.setTitle(task.getTitle());
                            taskEntity.setDescription(task.getDescription());
                            taskEntity.setCategoryId(Integer.parseInt(task.getCategory().getId()));
                            taskEntity.setPriority(task.getPriority());
                            taskEntity.setCompleted(taskShouldBeCompleted);
                            taskEntity.setCreatedAt(task.getCreatedAt());
                            taskEntity.setDueDate(task.getDueDate());
                            taskEntity.setStartTime(task.getStartTime());
                            taskEntity.setEndTime(task.getEndTime());
                            taskEntity.setUserId(authManager.getCurrentUserId());

                            todoViewModel.getRepository().updateTaskSync(taskEntity);

                            // Reset flag on main thread
                            if (getActivity() != null) {
                                getActivity().runOnUiThread(() -> {
                                    isUpdatingFromUI = false;
                                    android.util.Log.d("TaskListFragment", "Step update completed - setting isUpdatingFromUI = false");
                                });
                            }
                        } catch (NumberFormatException e) {
                            // Handle invalid ID and reset flag
                            if (getActivity() != null) {
                                getActivity().runOnUiThread(() -> {
                                    isUpdatingFromUI = false;
                                    android.util.Log.d("TaskListFragment", "Step update error - setting isUpdatingFromUI = false");
                                });
                            }
                        }
                    });
                }
            }

            @Override
            public void onStepEditClicked(int position, TaskStep step) {
                // Hiển thị dialog chỉnh sửa step
                showEditStepDialog(step, task, steps, () -> {
                    // Cập nhật adapter sau khi chỉnh sửa step
                    TaskStepDetailAdapter currentAdapter = (TaskStepDetailAdapter) recyclerViewStepsDetail.getAdapter();
                    if (currentAdapter != null) {
                        currentAdapter.updateSteps(steps);
                    }
                });
            }

            @Override
            public void onStepDeleteClicked(int position, TaskStep step) {
                // Delete step from database
                if (step.getId() != null) {
                    TodoDatabase.databaseWriteExecutor.execute(() -> {
                        try {
                            int stepId = Integer.parseInt(step.getId());
                            TaskStepEntity stepEntity = new TaskStepEntity();
                            stepEntity.setId(stepId);
                            todoViewModel.getRepository().deleteTaskStepSync(stepEntity);
                        } catch (NumberFormatException e) {
                            android.util.Log.e("TaskListFragment", "Invalid task ID: " + task.getId(), e);
                        } catch (Exception e) {
                            android.util.Log.e("TaskListFragment", "Error updating task: " + task.getId(), e);
                        }
                    });
                }
                steps.remove(position);
                updateStepsAdapter();

                // Update progress
                if (steps.isEmpty()) {
                    layoutProgress.setVisibility(View.GONE);
                    tvNoSteps.setVisibility(View.VISIBLE);
                } else {
                    int completedSteps = task.getCompletedStepsCount();
                    int totalSteps = task.getTotalStepsCount();
                    float progress = task.getProgressPercentage();

                    progressBar.setProgress((int) progress);
                    tvProgressText.setText(completedSteps + "/" + totalSteps + " bước hoàn thành");
                }

                Toast.makeText(getContext(), "Đã xóa bước", Toast.LENGTH_SHORT).show();
            }

            private void updateStepsAdapter() {
                TaskStepDetailAdapter currentAdapter = (TaskStepDetailAdapter) recyclerViewStepsDetail.getAdapter();
                if (currentAdapter != null) {
                    currentAdapter.updateSteps(steps);
                }
            }
        });

        // Thiết lập RecyclerView cho steps trong dialog chi tiết task
        recyclerViewStepsDetail.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewStepsDetail.setAdapter(stepAdapter);
        recyclerViewStepsDetail.setNestedScrollingEnabled(true); // Bật nested scrolling để hoạt động tốt với NestedScrollView (tránh xung đột scroll)

        // Add step button click
        btnAddStepDetail.setOnClickListener(v -> {
            showAddStepDialog(task, steps, () -> {
                // Update adapter after adding step
                TaskStepDetailAdapter currentAdapter = (TaskStepDetailAdapter) recyclerViewStepsDetail.getAdapter();
                if (currentAdapter != null) {
                    currentAdapter.updateSteps(steps);
                }

                // Update progress
                int completedSteps = 0;
                for (TaskStep step : steps) {
                    if (step.isCompleted()) {
                        completedSteps++;
                    }
                }
                int totalSteps = steps.size();
                float progress = totalSteps > 0 ? (float) completedSteps / totalSteps * 100f : 0f;

                progressBar.setMax(100);
                progressBar.setProgress((int) progress);
                tvProgressText.setText(completedSteps + "/" + totalSteps + " bước hoàn thành");

                if (!steps.isEmpty()) {
                    layoutProgress.setVisibility(View.VISIBLE);
                    tvNoSteps.setVisibility(View.GONE);
                } else {
                    layoutProgress.setVisibility(View.GONE);
                    tvNoSteps.setVisibility(View.VISIBLE);
                }
            });
        });

        // Create and show dialog
        AlertDialog dialog = builder.setView(dialogView)
                .setTitle("Chi tiết nhiệm vụ")
                .setPositiveButton("Đóng", null)
                .create();

        dialog.show();
    }

    private void showAddStepDialog(Task task, List<TaskStep> steps, Runnable onStepAdded) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());

        // Create simple input dialog
        EditText etStepTitle = new EditText(getContext());
        etStepTitle.setHint("Nhập tên bước...");
        etStepTitle.setPadding(50, 30, 50, 30);

        builder.setTitle("Thêm bước mới")
                .setView(etStepTitle)
                .setPositiveButton("Thêm", (dialog, which) -> {
                    String stepTitle = etStepTitle.getText().toString().trim();
                    if (!stepTitle.isEmpty()) {
                        // Create new step
                        TaskStep newStep = new TaskStep();
                        newStep.setTitle(stepTitle);
                        newStep.setDescription("");
                        newStep.setCompleted(false);
                        newStep.setOrder(steps.size());
                        newStep.setCreatedAt(new Date());

                        // Add to database
                        if (task.getId() != null) {
                            TodoDatabase.databaseWriteExecutor.execute(() -> {
                                try {
                                    int taskId = Integer.parseInt(task.getId());
                                    TaskStepEntity stepEntity = new TaskStepEntity();
                                    stepEntity.setTaskId(taskId);
                                    stepEntity.setTitle(stepTitle);
                                    stepEntity.setDescription("");
                                    stepEntity.setCompleted(false);
                                    stepEntity.setStepOrder(steps.size());
                                    stepEntity.setCreatedAt(new Date());

                                    long stepId = todoViewModel.getRepository().insertTaskStepSync(stepEntity);
                                    newStep.setId(String.valueOf(stepId));

                                    // Update UI on main thread
                                    if (getActivity() != null) {
                                        getActivity().runOnUiThread(() -> {
                                            steps.add(newStep);
                                            if (onStepAdded != null) {
                                                onStepAdded.run();
                                            }
                                        });
                                    }
                                } catch (NumberFormatException e) {
                                    // Handle invalid task ID
                                }
                            });
                        }
                    }
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

    /**
     * Hiển thị dialog thêm danh mục mới
     */
    private void showAddCategoryDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_category, null);

        EditText etCategoryName = dialogView.findViewById(R.id.et_category_name);

        // Lựa chọn icon và màu sắc
        String[] selectedIcon = {"�"}; // Icon mặc định
        String[] selectedColor = {"#2196F3"}; // Màu mặc định

        // Thiết lập các nút icon
        int[] iconButtonIds = {
            R.id.btn_icon_work, R.id.btn_icon_personal, R.id.btn_icon_study,
            R.id.btn_icon_health, R.id.btn_icon_shopping, R.id.btn_icon_family,
            R.id.btn_icon_travel, R.id.btn_icon_food, R.id.btn_icon_hobby,
            R.id.btn_icon_finance, R.id.btn_icon_home, R.id.btn_icon_other
        };

        String[] icons = {"💼", "👤", "📚", "💪", "🛒", "👨‍👩‍👧‍👦", "✈", "🍽️", "🎨", "💰", "🏠", "📝"};

        // Thiết lập click listener cho từng icon button
        for (int i = 0; i < iconButtonIds.length; i++) {
            Button iconButton = dialogView.findViewById(iconButtonIds[i]);
            final String icon = icons[i];
            iconButton.setOnClickListener(v -> {
                selectedIcon[0] = icon;
                // Reset tất cả button selections
                for (int id : iconButtonIds) {
                    dialogView.findViewById(id).setSelected(false);
                }
                // Đặt trạng thái selected cho button được chọn
                iconButton.setSelected(true);
            });
        }

        // Đặt icon mặc định được chọn
        dialogView.findViewById(R.id.btn_icon_work).setSelected(true);

        // Setup color selection
        int[] colorViewIds = {
            R.id.color_blue, R.id.color_green, R.id.color_orange,
            R.id.color_red, R.id.color_purple, R.id.color_pink
        };

        String[] colors = {"#2196F3", "#4CAF50", "#FF9800", "#F44336", "#9C27B0", "#E91E63"};

        for (int i = 0; i < colorViewIds.length; i++) {
            View colorView = dialogView.findViewById(colorViewIds[i]);
            final String color = colors[i];
            colorView.setOnClickListener(v -> {
                selectedColor[0] = color;
                // Reset all color selections
                for (int id : colorViewIds) {
                    dialogView.findViewById(id).setSelected(false);
                }
                // Set selected state
                colorView.setSelected(true);
            });
        }

        // Set default selection
        dialogView.findViewById(R.id.color_blue).setSelected(true);

        builder.setView(dialogView)
                .setTitle("Thêm danh mục mới")
                .setPositiveButton("Thêm", (dialog, which) -> {
                    String categoryName = etCategoryName.getText().toString().trim();
                    if (!categoryName.isEmpty()) {
                        addCategoryToDatabase(categoryName, selectedIcon[0], selectedColor[0]);
                    } else {
                        Toast.makeText(getContext(), "Vui lòng nhập tên danh mục", Toast.LENGTH_SHORT).show();
                    }
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

    private void addCategoryToDatabase(String name, String icon, String color) {
        CategoryEntity categoryEntity = new CategoryEntity();
        categoryEntity.setName(name);
        categoryEntity.setIcon(icon);
        categoryEntity.setColor(color);
        categoryEntity.setUserId(authManager.getCurrentUserId()); // Set user ID for custom categories

        TodoDatabase.databaseWriteExecutor.execute(() -> {
            todoViewModel.getRepository().insertCategorySync(categoryEntity);

            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Đã thêm danh mục: " + name, Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    /**
     * Hiển thị dialog chỉnh sửa step
     * @param step Step cần chỉnh sửa
     * @param task Task chứa step
     * @param steps Danh sách tất cả steps
     * @param onStepUpdated Callback khi step được cập nhật
     */
    private void showEditStepDialog(TaskStep step, Task task, List<TaskStep> steps, Runnable onStepUpdated) {
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());

        // Tạo layout cho dialog
        LinearLayout layout = new LinearLayout(getContext());
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setPadding(50, 40, 50, 40);

        // Title input
        EditText etTitle = new EditText(getContext());
        etTitle.setHint("Tiêu đề bước");
        etTitle.setText(step.getTitle());
        layout.addView(etTitle);

        // Description input
        EditText etDescription = new EditText(getContext());
        etDescription.setHint("Mô tả bước (tùy chọn)");
        etDescription.setText(step.getDescription());
        etDescription.setLines(3);
        layout.addView(etDescription);

        builder.setView(layout)
                .setTitle("Chỉnh sửa bước")
                .setPositiveButton("Lưu", (dialog, which) -> {
                    String title = etTitle.getText().toString().trim();
                    String description = etDescription.getText().toString().trim();

                    if (!title.isEmpty()) {
                        // Cập nhật step
                        step.setTitle(title);
                        step.setDescription(description);

                        // Cập nhật trong database
                        if (step.getId() != null && task.getId() != null) {
                            TodoDatabase.databaseWriteExecutor.execute(() -> {
                                try {
                                    int stepId = Integer.parseInt(step.getId());
                                    int taskId = Integer.parseInt(task.getId());

                                    TaskStepEntity stepEntity = new TaskStepEntity();
                                    stepEntity.setId(stepId);
                                    stepEntity.setTaskId(taskId);
                                    stepEntity.setTitle(title);
                                    stepEntity.setDescription(description);
                                    stepEntity.setCompleted(step.isCompleted());
                                    stepEntity.setStepOrder(step.getOrder());
                                    stepEntity.setCreatedAt(step.getCreatedAt());

                                    todoViewModel.getRepository().updateTaskStepSync(stepEntity);

                                    // Cập nhật UI trên main thread
                                    if (getActivity() != null) {
                                        getActivity().runOnUiThread(() -> {
                                            if (onStepUpdated != null) {
                                                onStepUpdated.run();
                                            }
                                            Toast.makeText(getContext(), "Đã cập nhật bước!", Toast.LENGTH_SHORT).show();
                                        });
                                    }
                                } catch (NumberFormatException e) {
                                    // Xử lý lỗi ID không hợp lệ
                                    if (getActivity() != null) {
                                        getActivity().runOnUiThread(() -> {
                                            Toast.makeText(getContext(), "Lỗi cập nhật bước", Toast.LENGTH_SHORT).show();
                                        });
                                    }
                                }
                            });
                        }
                    } else {
                        Toast.makeText(getContext(), "Vui lòng nhập tiêu đề bước", Toast.LENGTH_SHORT).show();
                    }
                })
                .setNegativeButton("Hủy", null)
                .show();
    }

}

[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\item_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\item_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\dialog_add_category.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\dialog_add_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\dialog_task_detail.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\dialog_task_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\item_category.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\item_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\item_task_step_edit.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\item_task_step_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\activity_auth.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\activity_auth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\fragment_register.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\fragment_register.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\dialog_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\dialog_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\fragment_calendar.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\fragment_calendar.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-50:/layout/dialog_add_category.xml", "source": "com.example.myapplication.app-main-53:/layout/dialog_add_category.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\fragment_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\fragment_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\fragment_profile.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\item_task_step_detail.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\item_task_step_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\fragment_task_list.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\fragment_task_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-mergeDebugResources-50:\\layout\\dialog_add_task.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.6\\com.example.myapplication.app-main-53:\\layout\\dialog_add_task.xml"}]
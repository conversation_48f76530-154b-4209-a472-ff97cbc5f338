<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/background_selected" />
            <stroke android:width="4dp" android:color="@color/border_selected" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/background_light" />
            <stroke android:width="2dp" android:color="@color/primary" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <stroke android:width="1dp" android:color="@color/border_light" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>

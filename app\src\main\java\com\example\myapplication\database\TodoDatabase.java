package com.example.myapplication.database;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.example.myapplication.database.converters.DateConverter;
import com.example.myapplication.database.converters.PriorityConverter;
import com.example.myapplication.database.dao.CategoryDao;
import com.example.myapplication.database.dao.TaskDao;
import com.example.myapplication.database.dao.TaskStepDao;
import com.example.myapplication.database.dao.UserDao;
import com.example.myapplication.database.entities.CategoryEntity;
import com.example.myapplication.database.entities.TaskEntity;
import com.example.myapplication.database.entities.TaskStepEntity;
import com.example.myapplication.database.entities.UserEntity;
import com.example.myapplication.models.Task;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Database(
    entities = {TaskEntity.class, CategoryEntity.class, TaskStepEntity.class, UserEntity.class},
    version = 5,
    exportSchema = false
)
@TypeConverters({DateConverter.class, PriorityConverter.class})
public abstract class TodoDatabase extends RoomDatabase {

    // Abstract methods to get DAOs
    public abstract TaskDao taskDao();
    public abstract CategoryDao categoryDao();
    public abstract TaskStepDao taskStepDao();
    public abstract UserDao userDao();

    // Singleton instance
    private static volatile TodoDatabase INSTANCE;
    
    // Thread pool for database operations
    private static final int NUMBER_OF_THREADS = 4;
    public static final ExecutorService databaseWriteExecutor = 
        Executors.newFixedThreadPool(NUMBER_OF_THREADS);

    // Get database instance (Singleton pattern)
    public static TodoDatabase getDatabase(final Context context) {
        if (INSTANCE == null) {
            synchronized (TodoDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            TodoDatabase.class,
                            "todo_database"
                        )
                        .fallbackToDestructiveMigration()
                        .addCallback(roomDatabaseCallback)
                        .build();

                    // Force populate data if database is empty
                    databaseWriteExecutor.execute(() -> {
                        if (INSTANCE.categoryDao().getCategoryCount() == 0) {
                            populateInitialData();
                        }
                    });
                }
            }
        }
        return INSTANCE;
    }

    private static void populateInitialData() {
        // Populate categories
        CategoryDao categoryDao = INSTANCE.categoryDao();

        // Insert default categories
        CategoryEntity[] categories = {
            new CategoryEntity("Cá nhân", "👤", "#2196F3"),
            new CategoryEntity("Công việc", "💼", "#FF9800"),
            new CategoryEntity("Học tập", "📚", "#4CAF50"),
            new CategoryEntity("Sức khỏe", "💪", "#E91E63"),
            new CategoryEntity("Mua sắm", "🛒", "#9C27B0"),
            new CategoryEntity("Gia đình", "👨‍👩‍👧‍👦", "#FF5722")
        };

        for (CategoryEntity category : categories) {
            categoryDao.insertCategory(category);
        }
    }

    // Database callback to populate initial data
    private static RoomDatabase.Callback roomDatabaseCallback = new RoomDatabase.Callback() {
        @Override
        public void onCreate(@NonNull SupportSQLiteDatabase db) {
            super.onCreate(db);
            
            // Populate database with initial data in background thread
            databaseWriteExecutor.execute(() -> {
                // Populate categories
                CategoryDao categoryDao = INSTANCE.categoryDao();
                
                // Insert default categories
                CategoryEntity[] categories = {
                    new CategoryEntity("Cá nhân", "👤", "#2196F3"),
                    new CategoryEntity("Công việc", "💼", "#FF9800"),
                    new CategoryEntity("Học tập", "📚", "#4CAF50"),
                    new CategoryEntity("Sức khỏe", "💪", "#E91E63"),
                    new CategoryEntity("Mua sắm", "🛒", "#9C27B0"),
                    new CategoryEntity("Gia đình", "👨‍👩‍👧‍👦", "#FF5722")
                };
                
                for (CategoryEntity category : categories) {
                    categoryDao.insertCategory(category);
                }

                // Insert sample tasks
                TaskDao taskDao = INSTANCE.taskDao();
                
                TaskEntity[] sampleTasks = {
                    new TaskEntity("Họp team buổi sáng", "Thảo luận tiến độ dự án", 2, null, Task.Priority.HIGH),
                    new TaskEntity("Tập thể dục", "Chạy bộ 30 phút", 4, null, Task.Priority.MEDIUM),
                    new TaskEntity("Mua sắm", "Mua thực phẩm cho tuần", 5, null, Task.Priority.LOW),
                    new TaskEntity("Học tiếng Anh", "Ôn tập bài 5", 3, null, Task.Priority.MEDIUM)
                };
                
                for (TaskEntity task : sampleTasks) {
                    taskDao.insertTask(task);
                }
            });
        }
    };

    // Method to close database
    public static void closeDatabase() {
        if (INSTANCE != null) {
            INSTANCE.close();
            INSTANCE = null;
        }
    }

    // Method to clear all data (for testing purposes)
    public void clearAllTables() {
        databaseWriteExecutor.execute(() -> {
            taskDao().deleteAllTasks();
            // Note: We don't delete categories as they are reference data
        });
    }
}

// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogAddTaskBinding implements ViewBinding {
  @NonNull
  private final NestedScrollView rootView;

  @NonNull
  public final Button btnAddStep;

  @NonNull
  public final Button btnSelectEndDatetime;

  @NonNull
  public final Button btnSelectStartDatetime;

  @NonNull
  public final TextInputEditText etTaskDescription;

  @NonNull
  public final TextInputEditText etTaskTitle;

  @NonNull
  public final RecyclerView recyclerViewSteps;

  @NonNull
  public final Spinner spinnerCategory;

  @NonNull
  public final Spinner spinnerPriority;

  @NonNull
  public final TextView tvEndDatetime;

  @NonNull
  public final TextView tvStartDatetime;

  private DialogAddTaskBinding(@NonNull NestedScrollView rootView, @NonNull Button btnAddStep,
      @NonNull Button btnSelectEndDatetime, @NonNull Button btnSelectStartDatetime,
      @NonNull TextInputEditText etTaskDescription, @NonNull TextInputEditText etTaskTitle,
      @NonNull RecyclerView recyclerViewSteps, @NonNull Spinner spinnerCategory,
      @NonNull Spinner spinnerPriority, @NonNull TextView tvEndDatetime,
      @NonNull TextView tvStartDatetime) {
    this.rootView = rootView;
    this.btnAddStep = btnAddStep;
    this.btnSelectEndDatetime = btnSelectEndDatetime;
    this.btnSelectStartDatetime = btnSelectStartDatetime;
    this.etTaskDescription = etTaskDescription;
    this.etTaskTitle = etTaskTitle;
    this.recyclerViewSteps = recyclerViewSteps;
    this.spinnerCategory = spinnerCategory;
    this.spinnerPriority = spinnerPriority;
    this.tvEndDatetime = tvEndDatetime;
    this.tvStartDatetime = tvStartDatetime;
  }

  @Override
  @NonNull
  public NestedScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogAddTaskBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogAddTaskBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_add_task, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogAddTaskBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_add_step;
      Button btnAddStep = ViewBindings.findChildViewById(rootView, id);
      if (btnAddStep == null) {
        break missingId;
      }

      id = R.id.btn_select_end_datetime;
      Button btnSelectEndDatetime = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectEndDatetime == null) {
        break missingId;
      }

      id = R.id.btn_select_start_datetime;
      Button btnSelectStartDatetime = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectStartDatetime == null) {
        break missingId;
      }

      id = R.id.et_task_description;
      TextInputEditText etTaskDescription = ViewBindings.findChildViewById(rootView, id);
      if (etTaskDescription == null) {
        break missingId;
      }

      id = R.id.et_task_title;
      TextInputEditText etTaskTitle = ViewBindings.findChildViewById(rootView, id);
      if (etTaskTitle == null) {
        break missingId;
      }

      id = R.id.recycler_view_steps;
      RecyclerView recyclerViewSteps = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewSteps == null) {
        break missingId;
      }

      id = R.id.spinner_category;
      Spinner spinnerCategory = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCategory == null) {
        break missingId;
      }

      id = R.id.spinner_priority;
      Spinner spinnerPriority = ViewBindings.findChildViewById(rootView, id);
      if (spinnerPriority == null) {
        break missingId;
      }

      id = R.id.tv_end_datetime;
      TextView tvEndDatetime = ViewBindings.findChildViewById(rootView, id);
      if (tvEndDatetime == null) {
        break missingId;
      }

      id = R.id.tv_start_datetime;
      TextView tvStartDatetime = ViewBindings.findChildViewById(rootView, id);
      if (tvStartDatetime == null) {
        break missingId;
      }

      return new DialogAddTaskBinding((NestedScrollView) rootView, btnAddStep, btnSelectEndDatetime,
          btnSelectStartDatetime, etTaskDescription, etTaskTitle, recyclerViewSteps,
          spinnerCategory, spinnerPriority, tvEndDatetime, tvStartDatetime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

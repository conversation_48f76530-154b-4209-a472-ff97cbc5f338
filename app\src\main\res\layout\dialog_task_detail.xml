<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:maxHeight="700dp"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Task Title -->
        <TextView
            android:id="@+id/tv_task_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Tiêu đề nhiệm vụ"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            android:layout_marginBottom="8dp" />

        <!-- Task Description -->
        <TextView
            android:id="@+id/tv_task_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Mô tả nhiệm vụ"
            android:textSize="14sp"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp"
            android:visibility="gone" />

        <!-- Task Info Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <!-- Category Chip -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Danh mục"
                android:layout_marginEnd="8dp"
                app:chipBackgroundColor="@color/primary"
                app:chipStrokeWidth="0dp" />

            <!-- Priority Chip -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chip_priority"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ưu tiên"
                app:chipBackgroundColor="@color/teal_200"
                app:chipStrokeWidth="0dp" />

        </LinearLayout>

        <!-- Time Information Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Thời gian"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_start_time_detail"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="📅 Bắt đầu: --/--/-- --:--"
                    android:textSize="12sp"
                    android:padding="8dp"
                    android:background="@drawable/bg_input_field"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/tv_end_time_detail"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="📅 Kết thúc: --/--/-- --:--"
                    android:textSize="12sp"
                    android:padding="8dp"
                    android:background="@drawable/bg_input_field"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Progress Section -->
        <LinearLayout
            android:id="@+id/layout_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tiến độ hoàn thành"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <ProgressBar
                android:id="@+id/progress_bar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:layout_marginBottom="4dp"
                android:progressTint="@color/primary" />

            <TextView
                android:id="@+id/tv_progress_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0/0 bước hoàn thành"
                android:textSize="12sp"
                android:textColor="@color/black" />

        </LinearLayout>

        <!-- Steps Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Các bước thực hiện"
                android:textStyle="bold"
                android:textSize="16sp" />

            <Button
                android:id="@+id/btn_add_step_detail"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="+ Thêm bước"
                android:textSize="12sp"
                android:backgroundTint="@color/primary"
                android:textColor="@android:color/white"
                style="@style/Widget.Material3.Button" />

        </LinearLayout>

        <!-- Steps RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_steps_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:nestedScrollingEnabled="true" />

        <!-- Empty State -->
        <TextView
            android:id="@+id/tv_no_steps"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Chưa có bước nào được thêm"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:gravity="center"
            android:padding="20dp"
            android:visibility="gone" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>

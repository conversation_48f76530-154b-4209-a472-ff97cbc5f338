package com.example.myapplication.database.entities;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.TypeConverters;

import com.example.myapplication.database.converters.DateConverter;
import com.example.myapplication.database.converters.PriorityConverter;
import com.example.myapplication.models.Task;
import com.example.myapplication.models.Category;

import java.util.Date;

@Entity(tableName = "tasks")
@TypeConverters({DateConverter.class, PriorityConverter.class})
public class TaskEntity {
    
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    @ColumnInfo(name = "title")
    private String title;
    
    @ColumnInfo(name = "description")
    private String description;
    
    @ColumnInfo(name = "category_id")
    private int categoryId;

    @ColumnInfo(name = "user_id")
    private int userId;
    
    @ColumnInfo(name = "due_date")
    private Date dueDate;

    @ColumnInfo(name = "start_time")
    private Date startTime;

    @ColumnInfo(name = "end_time")
    private Date endTime;

    @ColumnInfo(name = "priority")
    private Task.Priority priority;
    
    @ColumnInfo(name = "is_completed")
    private boolean isCompleted;
    
    @ColumnInfo(name = "created_at")
    private Date createdAt;
    
    @ColumnInfo(name = "updated_at")
    private Date updatedAt;

    // Constructors
    public TaskEntity() {
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    public TaskEntity(String title, String description, int categoryId, Date dueDate, Task.Priority priority) {
        this.title = title;
        this.description = description;
        this.categoryId = categoryId;
        this.dueDate = dueDate;
        this.priority = priority;
        this.isCompleted = false;
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
        this.updatedAt = new Date();
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = new Date();
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
        this.updatedAt = new Date();
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
        this.updatedAt = new Date();
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
        this.updatedAt = new Date();
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
        this.updatedAt = new Date();
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
        this.updatedAt = new Date();
    }

    public Task.Priority getPriority() {
        return priority;
    }

    public void setPriority(Task.Priority priority) {
        this.priority = priority;
        this.updatedAt = new Date();
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        isCompleted = completed;
        this.updatedAt = new Date();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper method to check if task is overdue
    public boolean isOverdue() {
        if (dueDate == null || isCompleted) {
            return false;
        }
        return dueDate.before(new Date());
    }

    // Convert to Task model
    public Task toTask() {
        Task task = new Task();
        task.setId(String.valueOf(this.id));
        task.setTitle(this.title);
        task.setDescription(this.description);
        task.setDueDate(this.dueDate);
        task.setStartTime(this.startTime);
        task.setEndTime(this.endTime);
        task.setPriority(this.priority);
        task.setCompleted(this.isCompleted);
        task.setCreatedAt(this.createdAt);
        // Note: Category will be set separately in the repository/fragment
        return task;
    }

    // Convert to Task model with Category
    public Task toTask(Category category) {
        Task task = toTask();
        task.setCategory(category);
        return task;
    }

    // Create from Task model
    public static TaskEntity fromTask(Task task) {
        TaskEntity entity = new TaskEntity();
        if (task.getId() != null && !task.getId().isEmpty()) {
            try {
                entity.setId(Integer.parseInt(task.getId()));
            } catch (NumberFormatException e) {
                // ID will be auto-generated
            }
        }
        entity.setTitle(task.getTitle());
        entity.setDescription(task.getDescription());
        entity.setDueDate(task.getDueDate());
        entity.setStartTime(task.getStartTime());
        entity.setEndTime(task.getEndTime());
        entity.setPriority(task.getPriority());
        entity.setCompleted(task.isCompleted());
        entity.setCreatedAt(task.getCreatedAt());
        return entity;
    }
}

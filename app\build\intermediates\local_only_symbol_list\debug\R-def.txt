R_DEF: Internal format may change without notice
local
color accent
color background_light
color background_selected
color black
color border_light
color border_selected
color bottom_nav_color
color error
color primary
color primary_dark
color primary_light
color purple_200
color purple_500
color purple_700
color success
color teal_200
color teal_700
color warning
color white
dimen fab_margin
dimen match_parent
drawable auth_background
drawable bg_date_circle
drawable bg_input_field
drawable bg_selected_color
drawable bg_selected_date
drawable bg_step_status
drawable button_outline
drawable button_primary
drawable category_background
drawable circle_background
drawable color_selector_blue
drawable color_selector_green
drawable color_selector_orange
drawable color_selector_pink
drawable color_selector_purple
drawable color_selector_red
drawable header_gradient
drawable ic_add
drawable ic_app_logo
drawable ic_arrow_right
drawable ic_calendar
drawable ic_delete
drawable ic_drag_handle
drawable ic_edit
drawable ic_email
drawable ic_filter
drawable ic_help
drawable ic_info
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_lock
drawable ic_login_illustration
drawable ic_person
drawable ic_profile
drawable ic_search
drawable ic_settings
drawable ic_statistics
drawable ic_tasks
drawable icon_selector_background
drawable icon_selector_ripple
drawable priority_background
drawable priority_badge
drawable rounded_background
id FirstFragment
id SecondFragment
id action_FirstFragment_to_SecondFragment
id action_SecondFragment_to_FirstFragment
id action_settings
id bottom_navigation
id btn_add_category
id btn_add_step
id btn_add_step_detail
id btn_category_menu
id btn_delete_step
id btn_delete_step_detail
id btn_edit_step
id btn_icon_family
id btn_icon_finance
id btn_icon_food
id btn_icon_health
id btn_icon_hobby
id btn_icon_home
id btn_icon_other
id btn_icon_personal
id btn_icon_shopping
id btn_icon_study
id btn_icon_travel
id btn_icon_work
id btn_login
id btn_logout
id btn_register
id btn_select_end_datetime
id btn_select_start_datetime
id calendar_view
id cb_remember_me
id cb_show_incomplete_only
id cb_show_incomplete_only_calendar
id cb_step_completed
id cb_task_completed
id cb_terms
id chip_category
id chip_priority
id color_blue
id color_green
id color_orange
id color_pink
id color_purple
id color_red
id et_category_name
id et_confirm_password
id et_email
id et_full_name
id et_password
id et_search
id et_step_description
id et_step_title
id et_task_description
id et_task_title
id et_username
id fab_add_task
id fragment_container
id iv_drag_handle
id iv_user_avatar
id layout_logged_in
id layout_not_logged_in
id layout_progress
id menu_about
id menu_delete_category
id menu_edit_category
id menu_help
id menu_settings
id menu_statistics
id nav_calendar
id nav_graph
id nav_profile
id nav_tasks
id progress_bar
id recycler_view_day_tasks
id recycler_view_steps
id recycler_view_steps_detail
id recycler_view_tasks
id spinner_category
id spinner_category_filter
id spinner_priority
id tab_layout
id tv_category_icon
id tv_category_name
id tv_date_number
id tv_end_datetime
id tv_end_time_detail
id tv_forgot_password
id tv_no_steps
id tv_progress_text
id tv_selected_date
id tv_start_datetime
id tv_start_time_detail
id tv_step_description
id tv_step_status
id tv_step_title
id tv_task_category
id tv_task_count
id tv_task_description
id tv_task_end_time
id tv_task_priority
id tv_task_start_time
id tv_task_title
id tv_user_email
id tv_user_initials
id tv_user_name
id view_pager
id view_priority_indicator
layout activity_auth
layout activity_main
layout dialog_add_category
layout dialog_add_task
layout dialog_login
layout dialog_task_detail
layout fragment_calendar
layout fragment_login
layout fragment_profile
layout fragment_register
layout fragment_task_list
layout item_category
layout item_task
layout item_task_step_detail
layout item_task_step_edit
menu bottom_navigation_menu
menu category_menu
menu menu_main
mipmap ic_launcher
mipmap ic_launcher_round
navigation nav_graph
string action_settings
string add_category
string add_task
string app_name
string category_all
string category_personal
string category_work
string complete_task
string delete_task
string edit
string first_fragment_label
string hello_first_fragment
string hello_second_fragment
string nav_calendar
string nav_profile
string nav_tasks
string next
string previous
string search
string second_fragment_label
string status_completed
string status_in_progress
string status_pending
string task_detail
string task_manager_title
style CalendarViewCustom
style Theme.MyApplication
style Theme.MyApplication.AppBarOverlay
style Theme.MyApplication.NoActionBar
style Theme.MyApplication.PopupOverlay
xml backup_rules
xml data_extraction_rules

<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="#E91E63" />
            <stroke android:width="4dp" android:color="@color/black" />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#E91E63" />
            <stroke android:width="3dp" android:color="#666666" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <solid android:color="#E91E63" />
            <stroke android:width="2dp" android:color="@color/border_light" />
        </shape>
    </item>
</selector>

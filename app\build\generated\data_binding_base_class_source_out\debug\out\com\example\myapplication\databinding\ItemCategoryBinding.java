// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCategoryBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView btnCategoryMenu;

  @NonNull
  public final TextView tvCategoryIcon;

  @NonNull
  public final TextView tvCategoryName;

  @NonNull
  public final TextView tvTaskCount;

  private ItemCategoryBinding(@NonNull MaterialCardView rootView, @NonNull TextView btnCategoryMenu,
      @NonNull TextView tvCategoryIcon, @NonNull TextView tvCategoryName,
      @NonNull TextView tvTaskCount) {
    this.rootView = rootView;
    this.btnCategoryMenu = btnCategoryMenu;
    this.tvCategoryIcon = tvCategoryIcon;
    this.tvCategoryName = tvCategoryName;
    this.tvTaskCount = tvTaskCount;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCategoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCategoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_category, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCategoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_category_menu;
      TextView btnCategoryMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnCategoryMenu == null) {
        break missingId;
      }

      id = R.id.tv_category_icon;
      TextView tvCategoryIcon = ViewBindings.findChildViewById(rootView, id);
      if (tvCategoryIcon == null) {
        break missingId;
      }

      id = R.id.tv_category_name;
      TextView tvCategoryName = ViewBindings.findChildViewById(rootView, id);
      if (tvCategoryName == null) {
        break missingId;
      }

      id = R.id.tv_task_count;
      TextView tvTaskCount = ViewBindings.findChildViewById(rootView, id);
      if (tvTaskCount == null) {
        break missingId;
      }

      return new ItemCategoryBinding((MaterialCardView) rootView, btnCategoryMenu, tvCategoryIcon,
          tvCategoryName, tvTaskCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

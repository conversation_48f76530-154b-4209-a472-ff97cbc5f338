<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_add_category" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\dialog_add_category.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_add_category_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="240" endOffset="14"/></Target><Target id="@+id/et_category_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="14" startOffset="8" endLine="19" endOffset="45"/></Target><Target id="@+id/btn_icon_work" view="Button"><Expressions/><location startLine="37" startOffset="8" endLine="46" endOffset="38"/></Target><Target id="@+id/btn_icon_personal" view="Button"><Expressions/><location startLine="48" startOffset="8" endLine="57" endOffset="38"/></Target><Target id="@+id/btn_icon_study" view="Button"><Expressions/><location startLine="59" startOffset="8" endLine="68" endOffset="38"/></Target><Target id="@+id/btn_icon_health" view="Button"><Expressions/><location startLine="70" startOffset="8" endLine="79" endOffset="38"/></Target><Target id="@+id/btn_icon_shopping" view="Button"><Expressions/><location startLine="81" startOffset="8" endLine="90" endOffset="38"/></Target><Target id="@+id/btn_icon_family" view="Button"><Expressions/><location startLine="92" startOffset="8" endLine="101" endOffset="38"/></Target><Target id="@+id/btn_icon_travel" view="Button"><Expressions/><location startLine="103" startOffset="8" endLine="112" endOffset="38"/></Target><Target id="@+id/btn_icon_food" view="Button"><Expressions/><location startLine="114" startOffset="8" endLine="123" endOffset="38"/></Target><Target id="@+id/btn_icon_hobby" view="Button"><Expressions/><location startLine="125" startOffset="8" endLine="134" endOffset="38"/></Target><Target id="@+id/btn_icon_finance" view="Button"><Expressions/><location startLine="136" startOffset="8" endLine="145" endOffset="38"/></Target><Target id="@+id/btn_icon_home" view="Button"><Expressions/><location startLine="147" startOffset="8" endLine="156" endOffset="38"/></Target><Target id="@+id/btn_icon_other" view="Button"><Expressions/><location startLine="158" startOffset="8" endLine="167" endOffset="38"/></Target><Target id="@+id/color_blue" view="View"><Expressions/><location startLine="184" startOffset="8" endLine="191" endOffset="38"/></Target><Target id="@+id/color_green" view="View"><Expressions/><location startLine="193" startOffset="8" endLine="200" endOffset="38"/></Target><Target id="@+id/color_orange" view="View"><Expressions/><location startLine="202" startOffset="8" endLine="209" endOffset="38"/></Target><Target id="@+id/color_red" view="View"><Expressions/><location startLine="211" startOffset="8" endLine="218" endOffset="38"/></Target><Target id="@+id/color_purple" view="View"><Expressions/><location startLine="220" startOffset="8" endLine="227" endOffset="38"/></Target><Target id="@+id/color_pink" view="View"><Expressions/><location startLine="229" startOffset="8" endLine="236" endOffset="38"/></Target></Targets></Layout>